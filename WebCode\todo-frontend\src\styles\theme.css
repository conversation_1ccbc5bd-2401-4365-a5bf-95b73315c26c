/* 主题变量定义 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 浅色主题 */
  --bg-color: #ffffff;
  --bg-secondary: #f5f5f5;
  --text-color: #000000d9;
  --text-secondary: #00000073;
  --border-color: #d9d9d9;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-color: #141414;
  --bg-secondary: #1f1f1f;
  --text-color: #ffffffd9;
  --text-secondary: #ffffff73;
  --border-color: #434343;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
}

/* 基础样式 */
body {
  background-color: var(--bg-secondary);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 浅色主题样式 */
.theme-light {
  background-color: #f5f5f5;
  color: #000000d9;
}

.theme-light .ant-layout {
  background-color: #f5f5f5;
}

.theme-light .ant-layout-header {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.theme-light .ant-card {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 深色主题样式 */
.theme-dark {
  background-color: #141414;
  color: #ffffffd9;
}

.theme-dark .ant-layout {
  background-color: #141414;
}

.theme-dark .ant-layout-header {
  background-color: #1f1f1f;
  border-bottom: 1px solid #434343;
}

.theme-dark .ant-card {
  background-color: #1f1f1f;
  border: 1px solid #434343;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
}

.theme-dark .ant-input,
.theme-dark .ant-select-selector,
.theme-dark .ant-picker {
  background-color: #1f1f1f;
  border-color: #434343;
  color: #ffffffd9;
}

.theme-dark .ant-input:hover,
.theme-dark .ant-select-selector:hover,
.theme-dark .ant-picker:hover {
  border-color: var(--primary-color);
}

.theme-dark .ant-input:focus,
.theme-dark .ant-select-focused .ant-select-selector,
.theme-dark .ant-picker-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 紧凑模式 */
.compact-mode {
  font-size: 13px;
}

.compact-mode .ant-card {
  padding: 12px;
}

.compact-mode .ant-card-head {
  min-height: 40px;
  padding: 0 12px;
}

.compact-mode .ant-card-body {
  padding: 12px;
}

.compact-mode .ant-form-item {
  margin-bottom: 16px;
}

.compact-mode .ant-btn {
  height: 28px;
  padding: 0 12px;
  font-size: 13px;
}

.compact-mode .ant-input {
  padding: 4px 8px;
  font-size: 13px;
}

/* 主题切换动画 */
* {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease,
              box-shadow 0.3s ease;
}

/* 主色调应用 */
.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  opacity: 0.8;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* 主题预览效果 */
.theme-preview {
  padding: 16px;
  border-radius: 8px;
  margin: 8px 0;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.theme-preview.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.theme-preview-light {
  background-color: #ffffff;
  color: #000000d9;
}

.theme-preview-dark {
  background-color: #1f1f1f;
  color: #ffffffd9;
}

/* 颜色选择器样式增强 */
.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-preview {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-preview:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow);
}
