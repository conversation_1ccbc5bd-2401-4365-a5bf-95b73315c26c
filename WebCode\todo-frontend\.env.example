# ===========================================
# 前端环境变量配置示例
# 此文件用于前端 Vite 应用配置
# ===========================================

# API配置
VITE_API_BASE_URL=http://localhost:5006/api

# 应用信息
VITE_APP_TITLE=智能提醒事项管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=面向技术人员的智能任务管理和甘特图可视化平台

# 功能开关
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_PERFORMANCE_MONITORING=false

# 开发调试
VITE_DEBUG_MODE=false

# 第三方服务配置（示例配置，需要替换为实际值）
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here

# 主题配置
VITE_DEFAULT_THEME=light
VITE_DEFAULT_PRIMARY_COLOR=#1890ff

# 本地化配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_DEFAULT_TIMEZONE=Asia/Shanghai

# ===========================================
# 使用说明：
# 1. 复制此文件为 .env.local 进行本地开发配置
# 2. 生产环境使用 .env.production
# 3. 开发环境使用 .env.development
# ===========================================
