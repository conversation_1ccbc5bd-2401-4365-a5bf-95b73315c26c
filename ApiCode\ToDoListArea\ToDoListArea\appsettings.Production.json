{
  // 生产环境配置文件
  // 环境变量通过Docker Compose注入，格式：${VARIABLE_NAME}
  "ConnectionStrings": {
    "DefaultConnection": "Server=database,1433;Database=ToDoListArea;User Id=sa;Password=${DB_SA_PASSWORD};TrustServerCertificate=true;"
  },
  "JwtSettings": {
    "SecretKey": "${JWT_SECRET_KEY}",
    "Issuer": "${JWT_ISSUER}",
    "Audience": "${JWT_AUDIENCE}",
    "ExpiryInMinutes": 30,
    "RefreshTokenExpiryInDays": 7
  },
  "AllowedOrigins": [
    "${DOMAIN_NAME}",
    "https://${DOMAIN_NAME}"
  ],
  "SecuritySettings": {
    "EnableSecurityHeaders": true,
    "MaxRequestBodySize": 10485760,
    "RequestTimeoutSeconds": 30,
    "EnableRateLimiting": true,
    "RateLimitRequests": 100,
    "RateLimitWindow": "00:01:00"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Error",
      "ToDoListArea": "Information",
      "ToDoListArea.Middleware.SecurityHeadersMiddleware": "Warning",
      "ToDoListArea.Middleware.GlobalExceptionMiddleware": "Error",
      "ToDoListArea.Services.LoggingService": "Information",
      "ToDoListArea.Services.MetricsService": "Warning"
    },
    "Console": {
      "IncludeScopes": false,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    },
    "File": {
      "Path": "/app/logs/todolist-{Date}.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30,
      "FileSizeLimitBytes": 10485760,
      "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxConcurrentUpgradedConnections": 100,
      "MaxRequestBodySize": 10485760,
      "KeepAliveTimeout": "00:02:00",
      "RequestHeadersTimeout": "00:00:30"
    }
  }
}
