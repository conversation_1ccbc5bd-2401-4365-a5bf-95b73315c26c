#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  text-align: left;
}

.App {
  width: 100%;
  min-height: 100vh;
}

/* 自定义样式 */
.avatar-uploader .ant-upload {
  border: none !important;
  background: transparent !important;
}

.ant-layout-header {
  line-height: 64px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }

  .ant-layout-content {
    padding: 16px;
  }
}

/* 桌面端优化 */
@media (min-width: 769px) {
  .ant-layout-header {
    padding: 0 24px;
  }

  .ant-layout-content {
    padding: 24px;
  }

  /* 确保登录卡片在桌面端正确显示 */
  .ant-card {
    max-width: none;
  }
}
