# ===========================================
# Docker编排环境变量配置示例
# 此文件用于Docker Compose和后端服务配置
# ===========================================

# 数据库配置
DB_SA_PASSWORD=TodoList@2024!

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production
JWT_ISSUER=ToDoListArea
JWT_AUDIENCE=ToDoListArea-Users
JWT_EXPIRATION=60

# 后端应用环境
ASPNETCORE_ENVIRONMENT=Production

# SSL证书配置（生产环境）
SSL_CERT_PATH=/etc/ssl/certs/todolist.crt
SSL_KEY_PATH=/etc/ssl/private/todolist.key

# 域名配置（后端服务）
DOMAIN_NAME=localhost

# 邮件服务配置（后端服务）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 监控配置
ENABLE_HEALTH_CHECKS=true
ENABLE_METRICS=true
LOG_LEVEL=Information

# Redis配置
REDIS_CONNECTION_STRING=localhost:6379

# ===========================================
# 注意：前端相关配置已移至 WebCode/todo-frontend/.env.example
# ===========================================
