# ===========================================
# Docker Compose - 适配现有环境配置
# 专为已有SQL Server、Nginx、Docker的环境设计
# ===========================================

version: '3.8'

services:
  # 后端API服务 - 连接现有SQL Server
  backend:
    build:
      context: ./ApiCode/ToDoListArea/ToDoListArea
      dockerfile: Dockerfile
    container_name: todolist-backend
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5006
      # 连接到现有的SQL Server (主机网络)
      - ConnectionStrings__DefaultConnection=Server=host.docker.internal,1433;Database=ToDoListArea;User Id=sa;Password=${DB_SA_PASSWORD};TrustServerCertificate=true;
      - JwtSettings__SecretKey=${JWT_SECRET_KEY}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - JwtSettings__ExpirationInMinutes=${JWT_EXPIRATION}
      # 生产环境特定配置
      - TZ=Asia/Shanghai
      - DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
    ports:
      - "5006:5006"
    volumes:
      # 生产环境日志
      - ./logs:/app/logs
      # 配置文件挂载
      - ./ApiCode/ToDoListArea/ToDoListArea/appsettings.Production.json:/app/appsettings.Production.json:ro
    networks:
      - todolist-network
    restart: unless-stopped
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    # Linux系统优化
    sysctls:
      - net.core.somaxconn=65535
    ulimits:
      nofile:
        soft: 65535
        hard: 65535
    # 生产环境健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5006/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端服务 - 不绑定80/443端口，由现有Nginx代理
  frontend:
    build:
      context: ./WebCode/todo-frontend
      dockerfile: Dockerfile
      target: production
    container_name: todolist-frontend
    environment:
      - NGINX_HOST=${DOMAIN_NAME:-localhost}
      - TZ=Asia/Shanghai
    ports:
      # 使用内部端口，避免与现有Nginx冲突
      - "8080:80"
    volumes:
      # Nginx日志
      - ./logs/nginx:/var/log/nginx
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - todolist-network
    restart: unless-stopped
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    # Linux系统优化
    sysctls:
      - net.core.somaxconn=65535
      - net.ipv4.tcp_max_syn_backlog=65535
    ulimits:
      nofile:
        soft: 65535
        hard: 65535
    # 生产环境健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis缓存服务 - 可选，如果现有环境没有Redis
  redis:
    image: redis:7-alpine
    container_name: todolist-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    networks:
      - todolist-network
    restart: unless-stopped
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    # 生产环境健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local

networks:
  todolist-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===========================================
# 使用说明：
# 1. 此配置适配现有SQL Server、Nginx、Docker环境
# 2. 前端服务使用8080端口，需要现有Nginx代理
# 3. 后端连接到主机的SQL Server (1433端口)
# 4. 不包含数据库服务，使用现有SQL Server
# ===========================================
