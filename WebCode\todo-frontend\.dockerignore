# ===========================================
# 前端Docker构建忽略文件
# 优化构建性能，减少镜像大小
# ===========================================

# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build
.output
.nuxt

# 开发工具
.vscode
.idea
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖锁定文件（保留package-lock.json用于构建）
# yarn.lock

# 环境变量文件
.env
.env.local
.env.*.local

# 缓存目录
.cache
.parcel-cache
.next

# 测试文件
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档文件
README.md
CHANGELOG.md
LICENSE

# Git相关
.git
.gitignore

# Docker相关
Dockerfile*
docker-compose*
.dockerignore

# 临时文件
*.tmp
*.temp
