{"ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=ToDoListArea;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "JwtSettings": {"SecretKey": "ToDoListArea_JWT_Secret_Key_2025_Very_Long_And_Secure_Key_For_Production_Use", "Issuer": "ToDoListArea", "Audience": "ToDoListArea_Users", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "AllowedOrigins": ["https://localhost", "https://your-domain.com"], "SecuritySettings": {"EnableSecurityHeaders": true, "MaxRequestBodySize": 10485760, "RequestTimeoutSeconds": 30, "EnableRateLimiting": true, "RateLimitRequests": 100, "RateLimitWindow": "00:01:00"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "ToDoListArea.Middleware.SecurityHeadersMiddleware": "Information", "ToDoListArea.Middleware.GlobalExceptionMiddleware": "Warning"}}, "AllowedHosts": "*"}